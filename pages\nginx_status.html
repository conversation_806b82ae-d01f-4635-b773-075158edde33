<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx监控</title>
    <!-- 引入Bootstrap CSS -->
    <link rel="stylesheet" href="../libs/bootstrap/5.3.6/index.min.css">
    <!-- 引入FontAwesome -->
    <link rel="stylesheet" href="../libs/fontawesome/6.7.2/css/all.min.css">
    <style>
        /* 全局字体优化 */
        body {
            font-size: 0.875rem;
            line-height: 1.4;
            background-color: #f8f9fa;
        }

        /* 标题字体调整 */
        h1, .h1 { font-size: 1.5rem; font-weight: 600; }
        h2, .h2 { font-size: 1.25rem; font-weight: 600; }
        h5, .h5 { font-size: 0.9rem; font-weight: 600; }

        /* 容器优化 */
        .container {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            margin-top: 1rem;
            padding: 1.5rem;
        }

        /* 图表容器优化 */
        .chart-container {
            height: 320px;
            position: relative;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 0.375rem;
            border: 1px solid rgba(0,0,0,0.05);
        }

        /* 状态徽章优化 */
        .status-badge {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
            font-weight: 500;
            border-radius: 0.25rem;
        }

        /* 卡片优化 */
        .card {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .card-title {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        /* 按钮优化 */
        .btn {
            font-size: 0.8rem;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        /* 日志条目优化 */
        .log-entry {
            transition: all 0.2s ease;
            border-radius: 0.25rem;
            margin-bottom: 0.25rem;
            padding: 0.5rem;
            font-size: 0.8rem;
        }

        .log-entry:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        /* 统计数字优化 */
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #495057;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #6c757d;
            font-weight: 500;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            body { font-size: 0.8rem; }
            .container { padding: 1rem; margin-top: 0.5rem; }
            .chart-container { height: 250px; }
            .btn { font-size: 0.75rem; padding: 0.4rem 0.8rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题和返回按钮 -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h1 class="h2 mb-0"><i class="fas fa-network-wired me-2 text-primary"></i>Nginx监控</h1>
            <a href="dashboard.html" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-arrow-left me-1"></i> 返回面板
            </a>
        </div>

        <!-- 状态概览 -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body p-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <h5 class="card-title mb-0 me-3">服务状态</h5>
                                <span class="badge bg-success status-badge"><i class="fas fa-check-circle me-1"></i> 运行中</span>
                            </div>
                            <div class="text-muted">
                                <small><i class="fas fa-hashtag me-1"></i>PID: 1234</small>
                                <span class="mx-2">|</span>
                                <small><i class="fas fa-clock me-1"></i>启动: 2024-01-15 08:30</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态控制区 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body p-3">
                        <h5 class="card-title mb-3"><i class="fas fa-cogs me-2 text-secondary"></i>服务控制</h5>
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="button" class="btn btn-danger btn-sm">
                                <i class="fas fa-power-off me-1"></i> 停止
                            </button>
                            <button type="button" class="btn btn-warning btn-sm">
                                <i class="fas fa-rotate me-1"></i> 重启
                            </button>
                            <button type="button" class="btn btn-success btn-sm">
                                <i class="fas fa-sync-alt me-1"></i> 重载配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body p-3">
                        <h5 class="card-title mb-3"><i class="fas fa-info-circle me-2 text-info"></i>连接信息</h5>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-number text-primary">128</div>
                                <div class="stat-label">活跃连接</div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="fs-4 fw-bold">5.2k</div>
                                <div class="text-muted small">总请求</div>
                            </div>
                            <div class="col-4 text-center">
                                <div class="fs-4 fw-bold">12.5</div>
                                <div class="text-muted small">请求/秒</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区 -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="monitorTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="cpu-tab" data-bs-toggle="tab" data-bs-target="#cpu-panel" type="button" role="tab" aria-controls="cpu-panel" aria-selected="true">
                                    <i class="fas fa-chart-line me-1"></i> CPU占用
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="memory-tab" data-bs-toggle="tab" data-bs-target="#memory-panel" type="button" role="tab" aria-controls="memory-panel" aria-selected="false">
                                    <i class="fas fa-chart-pie me-1"></i> 内存使用
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="connections-tab" data-bs-toggle="tab" data-bs-target="#connections-panel" type="button" role="tab" aria-controls="connections-panel" aria-selected="false">
                                    <i class="fas fa-chart-bar me-1"></i> 连接数
                                </button>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="monitorTabsContent">
                            <!-- CPU占用面板 -->
                            <div class="tab-pane fade show active" id="cpu-panel" role="tabpanel" aria-labelledby="cpu-tab">
                                <div class="chart-container">
                                    <div class="d-flex justify-content-center align-items-center h-100">
                                        <div class="text-center">
                                            <i class="fas fa-chart-line fa-3x text-muted mb-2"></i>
                                            <p class="text-muted">加载CPU监控数据中...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 内存使用面板 -->
                            <div class="tab-pane fade" id="memory-panel" role="tabpanel" aria-labelledby="memory-tab">
                                <div class="chart-container">
                                    <div class="d-flex justify-content-center align-items-center h-100">
                                        <div class="text-center">
                                            <i class="fas fa-chart-pie fa-3x text-muted mb-2"></i>
                                            <p class="text-muted">加载内存监控数据中...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 连接数面板 -->
                            <div class="tab-pane fade" id="connections-panel" role="tabpanel" aria-labelledby="connections-tab">
                                <div class="chart-container">
                                    <div class="d-flex justify-content-center align-items-center h-100">
                                        <div class="text-center">
                                            <i class="fas fa-chart-bar fa-3x text-muted mb-2"></i>
                                            <p class="text-muted">加载连接数监控数据中...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 错误日志 -->
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0"><i class="fas fa-bug me-2"></i>最近错误</h5>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush" style="max-height: 350px; overflow-y: auto;">
                            <div class="list-group-item log-entry p-3">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 text-danger"><i class="fas fa-exclamation-circle me-1"></i> 404 Not Found</h6>
                                    <small>10分钟前</small>
                                </div>
                                <p class="mb-1 small">/var/www/html/notfound.html</p>
                                <small>客户端IP: *************</small>
                            </div>
                            <div class="list-group-item log-entry p-3">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 text-warning"><i class="fas fa-exclamation-triangle me-1"></i> 502 Bad Gateway</h6>
                                    <small>30分钟前</small>
                                </div>
                                <p class="mb-1 small">/api/users</p>
                                <small>客户端IP: *************</small>
                            </div>
                            <div class="list-group-item log-entry p-3">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 text-danger"><i class="fas fa-exclamation-circle me-1"></i> 403 Forbidden</h6>
                                    <small>1小时前</small>
                                </div>
                                <p class="mb-1 small">/var/www/html/private/</p>
                                <small>客户端IP: *************</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent text-center">
                        <a href="#" class="btn btn-sm btn-outline-primary">查看全部日志</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 虚拟主机状态 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><i class="fas fa-server me-2"></i>虚拟主机状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>域名</th>
                                        <th>状态</th>
                                        <th>请求数</th>
                                        <th>流量</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>www.example.com</td>
                                        <td><span class="badge bg-success"><i class="fas fa-check me-1"></i>正常</span></td>
                                        <td>2,451</td>
                                        <td>128 MB</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-info"><i class="fas fa-chart-line me-1"></i>详情</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>api.example.com</td>
                                        <td><span class="badge bg-success"><i class="fas fa-check me-1"></i>正常</span></td>
                                        <td>5,782</td>
                                        <td>356 MB</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-info"><i class="fas fa-chart-line me-1"></i>详情</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>blog.example.com</td>
                                        <td><span class="badge bg-warning"><i class="fas fa-exclamation me-1"></i>警告</span></td>
                                        <td>892</td>
                                        <td>78 MB</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-info"><i class="fas fa-chart-line me-1"></i>详情</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="../libs/bootstrap/5.3.6/index.min.js"></script>
    <script>
        // 服务控制按钮交互
        document.addEventListener('DOMContentLoaded', function() {
            // 停止按钮交互
            const stopBtn = document.querySelector('.btn-danger');
            stopBtn.addEventListener('click', function() {
                if(confirm('确定要停止Nginx服务吗？这将导致网站无法访问！')) {
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> 停止中...';
                    this.disabled = true;
                    // 模拟停止操作
                    setTimeout(() => {
                        document.querySelector('.status-badge').className = 'badge bg-danger status-badge';
                        document.querySelector('.status-badge').innerHTML = '<i class="fas fa-times-circle me-1"></i> 已停止';
                        this.innerHTML = '<i class="fas fa-power-off me-1"></i> 停止';
                        this.disabled = false;
                    }, 1500);
                }
            });

            // 重启按钮交互
            const restartBtn = document.querySelector('.btn-warning');
            restartBtn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> 重启中...';
                this.disabled = true;
                // 模拟重启操作
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-rotate me-1"></i> 重启';
                    this.disabled = false;
                    alert('Nginx服务已成功重启');
                }, 2000);
            });
        });
    </script>
</body>
</html>