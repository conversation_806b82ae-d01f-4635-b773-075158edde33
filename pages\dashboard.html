<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控面板</title>
    <!-- 引入Bootstrap CSS -->
    <link rel="stylesheet" href="../libs/bootstrap/5.3.6/index.min.css">
    <!-- 引入FontAwesome -->
    <link rel="stylesheet" href="../libs/fontawesome/6.7.2/css/all.min.css">
    <style>
        /* 全局字体优化 */
        body {
            font-size: 0.875rem;
            line-height: 1.4;
        }

        /* 标题字体调整 */
        h1, .h1 { font-size: 1.5rem; font-weight: 600; }
        h2, .h2 { font-size: 1.25rem; font-weight: 600; }
        h3, .h3 { font-size: 1.1rem; font-weight: 600; }
        h4, .h4 { font-size: 1rem; font-weight: 600; }
        h5, .h5 { font-size: 0.9rem; font-weight: 600; }
        h6, .h6 { font-size: 0.8rem; font-weight: 600; }

        /* 侧边栏优化 */
        .sidebar {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-right: none;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar h5 {
            color: white;
            font-size: 1rem;
            margin-bottom: 1.5rem;
        }

        .sidebar .list-group-item {
            background: transparent;
            border: none;
            padding: 0.5rem 0;
        }

        .sidebar .list-group-item a {
            color: rgba(255,255,255,0.9);
            font-size: 0.85rem;
            transition: all 0.3s ease;
            padding: 0.4rem 0.8rem;
            border-radius: 0.25rem;
            display: block;
        }

        .sidebar .list-group-item a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(5px);
        }

        .sidebar h6 {
            color: rgba(255,255,255,0.7);
            font-size: 0.75rem;
            margin-bottom: 0.8rem;
            margin-top: 1.5rem;
        }

        /* 状态指示器优化 */
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
            box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
        }

        .status-running {
            background-color: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
        }

        .status-stopped {
            background-color: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
        }

        .status-warning {
            background-color: #ffc107;
            box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
        }

        /* 卡片优化 */
        .card {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }

        .card-title {
            font-size: 0.9rem;
        }

        .card-text {
            font-size: 0.8rem;
            margin-bottom: 0.8rem;
        }

        .card-footer {
            font-size: 0.75rem;
            padding: 0.75rem 1rem;
        }

        /* 按钮优化 */
        .btn {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
            border-radius: 0.25rem;
            font-weight: 500;
        }

        .btn-sm {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        /* 进度条优化 */
        .progress {
            height: 6px;
            border-radius: 3px;
            background-color: rgba(0,0,0,0.05);
        }

        /* 导航标签优化 */
        .nav-tabs .nav-link {
            font-size: 0.8rem;
            padding: 0.5rem 1rem;
            border: none;
            color: #6c757d;
        }

        .nav-tabs .nav-link.active {
            background-color: white;
            border-bottom: 2px solid #0d6efd;
            color: #0d6efd;
        }

        /* 列表组优化 */
        .list-group-item {
            font-size: 0.8rem;
            padding: 0.6rem 1rem;
            border-color: rgba(0,0,0,0.05);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            body { font-size: 0.8rem; }
            .card-title { font-size: 0.85rem; }
            .btn { font-size: 0.75rem; padding: 0.35rem 0.7rem; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-none d-md-block sidebar p-3">
                <h5 class="text-center mb-4"><i class="fas fa-server me-2"></i>服务器监控</h5>
                <div class="recent-dirs mb-4">
                    <h6 class="text-muted"><i class="fas fa-history me-2"></i>最近访问</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item"><a href="nginx_status.html" class="text-decoration-none"><i class="fas fa-network-wired me-2"></i>Nginx状态</a></li>
                        <li class="list-group-item"><a href="file_manager.html" class="text-decoration-none"><i class="fas fa-folder me-2"></i>文件管理</a></li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-10 ms-sm-auto px-3 py-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h1 class="h2 mb-0"><i class="fas fa-tachometer-alt me-2 text-primary"></i>系统监控面板</h1>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm"><i class="fas fa-sync-alt me-1"></i>刷新</button>
                        <button class="btn btn-outline-secondary btn-sm"><i class="fas fa-cog me-1"></i>设置</button>
                    </div>
                </div>

                <!-- 服务状态卡片 -->
                <div class="row row-cols-1 row-cols-md-3 g-3 mb-3">
                    <!-- Nginx状态卡片 -->
                    <div class="col">
                        <div class="card h-100 border-start border-primary border-3">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="card-title mb-0"><i class="fas fa-network-wired me-2 text-primary"></i>Nginx服务</h5>
                                    <span class="status-indicator status-running" title="运行中"></span>
                                </div>
                                <p class="card-text text-muted mb-2">运行时间: 2天8小时</p>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">CPU: 65%</small>
                                    <small class="text-success">正常</small>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent p-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">刚刚更新</small>
                                    <a href="nginx_status.html" class="btn btn-sm btn-outline-primary"><i class="fas fa-arrow-right me-1"></i>详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- MySQL状态卡片 -->
                    <div class="col">
                        <div class="card h-100 border-start border-success border-3">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="card-title mb-0"><i class="fas fa-database me-2 text-success"></i>MySQL服务</h5>
                                    <span class="status-indicator status-running" title="运行中"></span>
                                </div>
                                <p class="card-text text-muted mb-2">连接数: 12/100</p>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 12%" aria-valuenow="12" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">连接: 12%</small>
                                    <small class="text-success">正常</small>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent p-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">刚刚更新</small>
                                    <a href="#" class="btn btn-sm btn-outline-success"><i class="fas fa-arrow-right me-1"></i>详情</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Redis状态卡片 -->
                    <div class="col">
                        <div class="card h-100 border-start border-warning border-3">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h5 class="card-title mb-0"><i class="fas fa-memory me-2 text-warning"></i>Redis服务</h5>
                                    <span class="status-indicator status-warning" title="警告"></span>
                                </div>
                                <p class="card-text text-muted mb-2">内存使用: 85%</p>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 85%" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">内存: 85%</small>
                                    <small class="text-warning">警告</small>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent p-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">刚刚更新</small>
                                    <a href="#" class="btn btn-sm btn-outline-warning"><i class="fas fa-arrow-right me-1"></i>详情</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 资源使用情况 -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-3">
                            <div class="card-header bg-white border-bottom">
                                <ul class="nav nav-tabs card-header-tabs mb-0">
                                    <li class="nav-item">
                                        <a class="nav-link active" href="#cpu-tab"><i class="fas fa-microchip me-1"></i>CPU占用</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#mem-tab"><i class="fas fa-memory me-1"></i>内存使用</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="#disk-tab"><i class="fas fa-hdd me-1"></i>磁盘IO</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body p-3">
                                <div class="chart-container" style="height: 280px;">
                                    <!-- 图表将通过JavaScript渲染 -->
                                    <div class="d-flex justify-content-center align-items-center h-100">
                                        <div class="text-center">
                                            <i class="fas fa-chart-line fa-2x text-muted mb-3"></i>
                                            <p class="text-muted mb-0">加载图表数据中...</p>
                                            <div class="spinner-border spinner-border-sm text-primary mt-2" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统信息 -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>系统信息</h5>
                            </div>
                            <div class="card-body p-0">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-server me-2 text-primary"></i>服务器</span>
                                        <span class="fw-bold text-dark">Linux 4.15.0</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-microchip me-2 text-success"></i>CPU</span>
                                        <span class="text-dark">4核 Intel Xeon</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-memory me-2 text-info"></i>内存</span>
                                        <span class="text-dark">16GB / 32GB</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-hdd me-2 text-warning"></i>磁盘</span>
                                        <span class="text-dark">120GB / 500GB</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-clock me-2 text-secondary"></i>运行时间</span>
                                        <span class="text-dark">5天12小时</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-footer bg-light text-center">
                                <small class="text-muted">最后更新: 2024-01-15 14:30</small>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 引入Bootstrap JS -->
    <script src="../libs/bootstrap/5.3.6/index.min.js"></script>
    <script>
        // 简单的页面交互示例
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 模拟状态刷新
            const refreshBtn = document.querySelector('.btn-outline-primary');
            refreshBtn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新';
                    // 这里可以添加实际的刷新逻辑
                }, 1000);
            });
        });
    </script>
</body>
</html>